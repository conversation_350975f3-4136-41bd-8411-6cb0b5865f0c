const { upsertDocument, sqlQuery } = require('../lib/manticore_http_client.js');
const { processSummaryTask } = require('../workers/summary_worker.js');
const { generateAIResponse } = require('./ai_response_service.js');
const telemetry = require('../lib/telemetry.js');

/**
 * In-memory recent messages cache (per-topic).
 * Purpose: provide immediate UI-visible messages without waiting for search/index consistency.
 * This is a short-term pragmatic fix to ensure messages appear in the UI right after send.
 * It is intentionally simple: keeps up to N recent messages per topic in memory.
 */
const RECENT_CACHE_LIMIT = 200;
const recentMessagesByTopic = new Map();

/**
 * produceMessage(topic_id, { role, content, message_id?, turn_id?, status? })
 * - formats a conversation document, attempts to persist to Manticore (best-effort)
 * - returns the persisted message object (or the generated object if persistence fails)
 */
async function produceMessage(topic_id, { role, content, message_id = null, turn_id = null, status = 'pending' }) {
  const now = new Date().toISOString();
  const msgId = message_id || Date.now();
  const turnId = turn_id || 1;

  // Align document shape with examples/manticore/init.sql:
  // - use message_id as the primary identifier (Manticore will auto-assign _id)
  // - include created_at TIMESTAMP field
  // - keep field names consistent with RT table
  const doc = {
    // Use message_id as the primary identifier - do not specify 'id' to avoid conflicts
    // with Manticore's internal _id field
    message_id: msgId,
    turn_id: turnId,
    topic_id: Number(topic_id),
    role: role || '',
    content: content || '',
    created_at: now,
    summary: '',
    summary_version: 0,
    status: status || 'pending',
    deleted: 0
  };

  // Best-effort persistence: attempt to upsert, but do not fail the caller on network/index errors
  try {
    // upsertDocument expects table name and doc
    await upsertDocument('conversations_rt', doc, true);
  } catch (err) {
    // Log the full error so 409 / schema issues are visible during debugging (do not rethrow)
    // eslint-disable-next-line no-console
    console.error('manticore upsert failed', err && err.response ? err.response.data || err.response.statusText : err);
  }

  return doc;
}

/**
 * updateMessageStatus(message_id, status)
 * - Updates the status of a specific message in Manticore
 * - Returns true if successful, false otherwise
 * @param {string|number} message_id - Message ID to update
 * @param {string} status - New status ('pending', 'completed', 'failed')
 * @returns {Promise<boolean>}
 */
async function updateMessageStatus(message_id, status) {
  // Validate status
  const validStatuses = ['pending', 'completed', 'failed'];
  if (!validStatuses.includes(status)) {
    throw new Error(`Invalid status: ${status}. Must be one of: ${validStatuses.join(', ')}`);
  }

  try {
    const sql = `UPDATE conversations_rt SET status='${status}' WHERE message_id=${Number(message_id)}`;
    const result = await sqlQuery(sql);

    // Check if update was successful
    const affected = (result && (result.affectedRows || result.affected_rows || result.rowsAffected)) || 0;
    return affected > 0;
  } catch (err) {
    console.error('Failed to update message status:', err);
    return false;
  }
}

/**
 * getMessageStatus(message_id)
 * - Retrieves the current status of a specific message
 * @param {string|number} message_id - Message ID to query
 * @returns {Promise<string|null>} Current status or null if not found
 */
async function getMessageStatus(message_id) {
  try {
    const sql = `SELECT status FROM conversations_rt WHERE message_id=${Number(message_id)} LIMIT 1`;
    const result = await sqlQuery(sql);

    // Handle different response formats from Manticore
    let rows = [];
    if (Array.isArray(result) && result.length > 0 && Array.isArray(result[0].rows)) {
      rows = result[0].rows;
    } else if (result && Array.isArray(result.rows)) {
      rows = result.rows;
    } else if (Array.isArray(result)) {
      rows = result;
    }

    if (rows && rows.length > 0 && rows[0].status) {
      return rows[0].status;
    }
    return null;
  } catch (err) {
    console.error('Failed to get message status:', err);
    return null;
  }
}

/**
 * enqueueRegenerateSummary(topic_id, { message_id, turn_id, content, summary_version })
 * - Fire-and-forget: triggers background processing for regenerating a summary for a given message/turn.
 * - Returns a Promise that resolves immediately (does not wait for processing to complete).
 * - In production this would enqueue into a real queue; for MVP we call the worker asynchronously.
 */
function enqueueRegenerateSummary(topic_id, { message_id = null, turn_id = null, content = '', summary_version = 0 } = {}) {
  // Build task payload expected by processSummaryTask
  const task = {
    message_id: message_id || null,
    turn_id: turn_id || null,
    topic_id: Number(topic_id),
    content,
    summary_version: Number(summary_version) || 0,
    attempt: 0
  };

  // Fire-and-forget invocation: call worker but do not await. Ensure exceptions are caught.
  (async () => {
    try {
      // If message_id is missing, we still attempt processing (worker may handle reading content)
      await processSummaryTask(task).catch((err) => {
        // swallow worker errors here; in real system we'd log telemetry
        // console.error('summary task failed', err);
      });
    } catch (err) {
      // ensure no unhandled rejection
      // console.error('background summary error', err);
    }
  })();

  return Promise.resolve({ enqueued: true, task });
}

/**
 * getMessages(topic_id)
 * - Retrieves all messages for a given topic from Manticore
 * - Returns an array of message objects
 */
async function getMessages(topic_id) {
  try {
    const sql = `SELECT * FROM conversations_rt WHERE topic_id=${Number(topic_id)} ORDER BY created_at ASC`;
    const result = await sqlQuery(sql);

    // Handle different response formats from Manticore
    let rows = [];
    if (Array.isArray(result) && result.length > 0 && Array.isArray(result[0].rows)) {
      rows = result[0].rows;
    } else if (result && Array.isArray(result.rows)) {
      rows = result.rows;
    } else if (Array.isArray(result)) {
      rows = result;
    }

    // Transform rows to message format expected by frontend
    return rows.map(row => ({
      id: row.message_id || row.id,
      message_id: row.message_id || row.id,
      role: row.role || 'user',
      content: row.content || '',
      created_at: row.created_at,
      turn_id: row.turn_id,
      topic_id: row.topic_id,
      status: row.status || 'completed' // Default to completed for backward compatibility
    }));
  } catch (err) {
    console.error('Failed to get messages from Manticore:', err);
    return [];
  }
}

/**
 * getSummaries(topic_id)
 * - Retrieves all summaries for a given topic from Manticore
 * - Returns an array of summary objects
 */
async function getSummaries(topic_id) {
  try {
    const sql = `SELECT * FROM conversations_rt WHERE topic_id=${Number(topic_id)} AND summary != '' ORDER BY created_at DESC`;
    const result = await sqlQuery(sql);

    // Handle different response formats from Manticore
    let rows = [];
    if (Array.isArray(result) && result.length > 0 && Array.isArray(result[0].rows)) {
      rows = result[0].rows;
    } else if (result && Array.isArray(result.rows)) {
      rows = result.rows;
    } else if (Array.isArray(result)) {
      rows = result;
    }

    // Transform rows to summary format expected by frontend
    return rows
      .filter(row => row.summary && row.summary.trim() !== '')
      .map(row => ({
        id: row.message_id || row.id,
        summary_id: row.message_id || row.id,
        message_id: row.message_id || row.id,
        text: row.summary,
        summary: row.summary,
        label: `摘要 ${row.message_id || row.id}`,
        title: `摘要 ${row.message_id || row.id}`,
        created_at: row.created_at,
        summary_version: row.summary_version || 0
      }));
  } catch (err) {
    console.error('Failed to get summaries from Manticore:', err);
    return [];
  }
}

/**
 * handleConversationFlow(topicId, message)
 * - Complete conversation flow: save user message → generate AI response → save AI response
 * - Handles turn management and message sequencing
 * - Returns both user and AI messages
 * @param {number} topicId - Topic ID for the conversation
 * @param {Object} message - User message object {id?, content, author?}
 * @returns {Promise<{userMessage: Object, aiMessage: Object}>}
 */
async function handleConversationFlow(topicId, message) {
  const startTime = Date.now();

  try {
    // 1. Generate turn_id for this conversation round
    const turnId = await allocateTurnId(topicId);

    // 2. Save user message with completed status (user messages are immediately completed)
    const userMessage = await produceMessage(topicId, {
      role: 'user',
      content: message.content,
      message_id: message.id || null,
      turn_id: turnId,
      status: 'completed'
    });

    telemetry.emit('conversation.user_message_saved', {
      topic_id: topicId,
      turn_id: turnId,
      message_id: userMessage.message_id,
      content_length: message.content.length
    });

    // 3. Create pending AI message first
    let aiMessage = await produceMessage(topicId, {
      role: 'assistant',
      content: '', // Empty content initially
      turn_id: turnId,
      status: 'pending'
    });

    // 4. Generate AI response asynchronously
    try {
      const aiResponse = await generateAIResponse(topicId, message.content, {
        includeContext: true,
        maxTokens: 2000
      });

      // 5. Update AI message with generated content and completed status
      const updateSql = `UPDATE conversations_rt SET content='${aiResponse.content.replace(/'/g, "''")}', status='completed' WHERE message_id=${aiMessage.message_id}`;
      await sqlQuery(updateSql);

      // Update local aiMessage object
      aiMessage.content = aiResponse.content;
      aiMessage.status = 'completed';

      telemetry.emit('conversation.ai_response_saved', {
        topic_id: topicId,
        turn_id: turnId,
        message_id: aiMessage.message_id,
        content_length: aiResponse.content.length,
        tokens_used: aiResponse.metadata.tokens_used,
        duration_ms: Date.now() - startTime,
        fallback_used: aiResponse.metadata.fallback || false
      });

    } catch (aiError) {
      // If AI response fails, log error and update status to failed
      telemetry.emit('conversation.ai_response_failed', {
        topic_id: topicId,
        turn_id: turnId,
        error: aiError.message,
        duration_ms: Date.now() - startTime
      });

      // Update AI message with fallback content and failed status
      const fallbackContent = '抱歉，我现在遇到了一些技术问题，请稍后再试。';
      const updateSql = `UPDATE conversations_rt SET content='${fallbackContent}', status='failed' WHERE message_id=${aiMessage.message_id}`;
      await sqlQuery(updateSql);

      // Update local aiMessage object
      aiMessage.content = fallbackContent;
      aiMessage.status = 'failed';
    }

    // 5. Check if summary should be triggered (every 10 turns or configurable)
    if (turnId % 10 === 0) {
      try {
        enqueueRegenerateSummary(topicId, {
          message_id: aiMessage.message_id,
          turn_id: turnId,
          content: aiMessage.content
        });

        telemetry.emit('conversation.summary_triggered', {
          topic_id: topicId,
          turn_id: turnId,
          trigger_reason: 'turn_interval'
        });
      } catch (summaryError) {
        // Summary generation is optional, don't fail the main flow
        telemetry.emit('conversation.summary_trigger_failed', {
          topic_id: topicId,
          turn_id: turnId,
          error: summaryError.message
        });
      }
    }

    telemetry.emit('conversation.flow_completed', {
      topic_id: topicId,
      turn_id: turnId,
      total_duration_ms: Date.now() - startTime,
      success: true
    });

    return {
      userMessage,
      aiMessage,
      turnId,
      metadata: {
        duration_ms: Date.now() - startTime,
        summary_triggered: turnId % 10 === 0
      }
    };

  } catch (error) {
    telemetry.emit('conversation.flow_failed', {
      topic_id: topicId,
      error: error.message,
      duration_ms: Date.now() - startTime
    });

    throw error;
  }
}

/**
 * allocateTurnId(topicId)
 * - Allocates a new turn_id for the topic
 * - For now, uses a simple increment based on existing messages
 * - TODO: Implement DB-backed allocation with proper concurrency handling
 * @param {number} topicId - Topic ID
 * @returns {Promise<number>} New turn_id
 */
async function allocateTurnId(topicId) {
  // DB-backed optimistic allocation with retry loop.
  // Strategy:
  // 1) Try to update an entry in a lightweight counter table `turn_counters`:
  //    - If row exists: attempt to UPDATE last_turn = last_turn + 1 WHERE topic_id = ? AND last_turn = <observed>
  //    - If row does not exist: INSERT a row with last_turn = 1
  // 2) Use a small retry loop to handle contention. This avoids large table scans (SELECT MAX).
  //
  // Note: This implementation assumes the SQL endpoint (`sqlQuery`) supports basic SELECT/UPDATE/INSERT
  // and returns result metadata in a shape we can introspect. If the database does not have a
  // `turn_counters` table, the code will attempt to create it on demand (best-effort).
  const MAX_ALLOCATE_RETRIES = 5;
  const topic = Number(topicId);

  try {
    // Ensure turn_counters table exists (best-effort). This is idempotent in many SQL dialects.
    try {
      const createSql = `CREATE TABLE IF NOT EXISTS turn_counters (topic_id INT PRIMARY KEY, last_turn INT)`;
      await sqlQuery(createSql);
    } catch (e) {
      // ignore creation errors — many deployments may not allow DDL. We'll fall back to SELECT MAX approach below.
    }

    for (let attempt = 1; attempt <= MAX_ALLOCATE_RETRIES; attempt++) {
      try {
        // Try to read current counter
        const selSql = `SELECT last_turn FROM turn_counters WHERE topic_id=${topic} LIMIT 1`;
        const selRes = await sqlQuery(selSql);

        // Normalize rows extraction
        let rows = [];
        if (Array.isArray(selRes) && selRes.length > 0 && Array.isArray(selRes[0].rows)) {
          rows = selRes[0].rows;
        } else if (selRes && Array.isArray(selRes.rows)) {
          rows = selRes.rows;
        } else if (Array.isArray(selRes) && selRes.length > 0) {
          rows = selRes;
        }

        if (rows && rows.length > 0 && typeof rows[0].last_turn !== 'undefined') {
          const current = Number(rows[0].last_turn) || 0;
          const next = current + 1;

          // Attempt optimistic update: only succeed if last_turn still equals current
          const updateSql = `UPDATE turn_counters SET last_turn=${next} WHERE topic_id=${topic} AND last_turn=${current}`;
          const updRes = await sqlQuery(updateSql);

          // Heuristic: some connectors return affected rows as integer, others return result objects.
          const affected = (updRes && (updRes.affectedRows || updRes.affected_rows || updRes.rowsAffected)) || 0;
          if (affected === 0) {
            // contention — retry after small backoff
            await new Promise(r => setTimeout(r, 20 * attempt));
            continue;
          }

          return next;
        } else {
          // No counter row exists — try to insert one with last_turn = 1
          try {
            const insertSql = `INSERT INTO turn_counters (topic_id, last_turn) VALUES (${topic}, 1)`;
            await sqlQuery(insertSql);
            return 1;
          } catch (insertErr) {
            // Insert may fail due to race (another thread inserted concurrently) — retry
            await new Promise(r => setTimeout(r, 20 * attempt));
            continue;
          }
        }
      } catch (innerErr) {
        // On SQL read/update error, retry with small backoff
        await new Promise(r => setTimeout(r, 20 * attempt));
        continue;
      }
    }

    // Fallback: if optimistic counter approach failed (e.g., no DDL permission), fall back to SELECT MAX approach
    try {
      const sql = `SELECT MAX(turn_id) as max_turn FROM conversations_rt WHERE topic_id=${topic}`;
      const result = await sqlQuery(sql);
      let maxTurn = 0;
      if (Array.isArray(result) && result.length > 0 && Array.isArray(result[0].rows)) {
        const rows2 = result[0].rows;
        if (rows2.length > 0 && rows2[0].max_turn) {
          maxTurn = Number(rows2[0].max_turn) || 0;
        }
      } else if (result && Array.isArray(result.rows)) {
        const rows2 = result.rows;
        if (rows2.length > 0 && rows2[0].max_turn) {
          maxTurn = Number(rows2[0].max_turn) || 0;
        }
      } else if (Array.isArray(result) && result.length > 0) {
        if (result[0].max_turn) {
          maxTurn = Number(result[0].max_turn) || 0;
        }
      }
      return maxTurn + 1;
    } catch (fallbackErr) {
      console.error('Failed to allocate turn_id (fallback):', fallbackErr);
      return 1;
    }
  } catch (error) {
    console.error('Failed to allocate turn_id, using default:', error);
    return 1;
  }
}

module.exports = {
  produceMessage,
  enqueueRegenerateSummary,
  getMessages,
  getSummaries,
  handleConversationFlow,
  allocateTurnId,
  updateMessageStatus,
  getMessageStatus
};