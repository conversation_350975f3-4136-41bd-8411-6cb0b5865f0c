/*
src/services/ai_response_service.js

AI Response Service - generates intelligent responses using OpenAI API

Exports:
- generateAIResponse(topicId, userMessage, options) -> { content, metadata }
- buildConversationContext(topicId, maxMessages) -> { messages, summary }

Features:
- OpenAI API integration with error handling
- Conversation context building
- Retry logic with exponential backoff
- Fallback responses for failures
- Telemetry integration
*/

const { getOpenAIClient } = require('../lib/openai_client');
const { searchContext } = require('./retrieval_service');
const telemetry = require('../lib/telemetry');

// Configuration constants
const DEFAULT_MODEL = process.env.OPENAI_MODEL || 'gpt-4o-mini';
const DEFAULT_MAX_TOKENS = Number(process.env.OPENAI_MAX_TOKENS || 1000);
const DEFAULT_TEMPERATURE = Number(process.env.OPENAI_TEMPERATURE || 0.7);
const MAX_CONTEXT_MESSAGES = Number(process.env.MAX_CONTEXT_MESSAGES || 10);
const AI_RESPONSE_TIMEOUT = Number(process.env.AI_RESPONSE_TIMEOUT || 30000);
const MAX_RETRIES = 3;

// System prompt for the AI assistant
const SYSTEM_PROMPT = `你是一个有帮助的AI学习助手。你的任务是：
1. 根据用户的问题提供准确、有用的回答
2. 保持对话的连贯性，参考之前的对话历史
3. 用简洁明了的语言解释复杂概念
4. 鼓励用户深入思考和学习
5. 如果不确定答案，诚实地说明并建议进一步研究的方向

请用中文回复，保持友好和专业的语调。`;

// Fallback responses for when AI generation fails
const FALLBACK_RESPONSES = [
  "抱歉，我现在遇到了一些技术问题。请稍后再试，或者可以重新表述您的问题。",
  "让我重新整理一下思路。系统正在处理中，请稍等片刻。",
  "我暂时无法生成回复。请检查网络连接或稍后重试。",
  "系统繁忙，请稍后再试。您也可以尝试简化问题重新提问。"
];

/**
 * Error types for AI response generation
 */
const AI_ERROR_TYPES = {
  RATE_LIMIT: 'rate_limit',
  API_ERROR: 'api_error', 
  TIMEOUT: 'timeout',
  CONTEXT_TOO_LONG: 'context_too_long',
  NETWORK_ERROR: 'network_error',
  INVALID_RESPONSE: 'invalid_response'
};

/**
 * Estimate token count for text (rough approximation)
 * @param {string} text - Text to estimate tokens for
 * @returns {number} Estimated token count
 */
function estimateTokenCount(text) {
  if (!text || typeof text !== 'string') return 0;
  // Rough approximation: 1 token ≈ 4 characters for English, 2-3 for Chinese
  // Use conservative estimate of 2.5 characters per token
  return Math.ceil(text.length / 2.5);
}

/**
 * Truncate messages to fit within token limit
 * @param {Array} messages - Array of message objects
 * @param {number} maxTokens - Maximum tokens allowed
 * @returns {Array} Truncated messages array
 */
function truncateMessagesToTokenLimit(messages, maxTokens) {
  if (!messages || messages.length === 0) return [];

  const truncated = [];
  let totalTokens = 0;

  // Process messages from most recent to oldest
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i];
    const messageTokens = estimateTokenCount(message.content);

    // Always include at least one message (the most recent)
    if (totalTokens + messageTokens <= maxTokens || truncated.length === 0) {
      truncated.unshift(message);
      totalTokens += messageTokens;
    } else {
      break;
    }
  }

  return truncated;
}

/**
 * Build conversation context from recent messages
 * @param {number} topicId - Topic ID to get context for
 * @param {number} maxMessages - Maximum number of messages to include
 * @param {Object} options - Additional options
 * @returns {Promise<{messages: Array, summary: string, metadata: Object}>}
 */
async function buildConversationContext(topicId, maxMessages = MAX_CONTEXT_MESSAGES, options = {}) {
  const {
    maxTokens = 2000, // Reserve tokens for context (out of total model limit)
    includeSystemPrompt = false
  } = options;

  try {
    // Use retrieval service to get recent conversation messages
    const result = await searchContext({
      topic_id: topicId,
      query: null, // No specific query, just get recent messages
      top_k: maxMessages * 2, // Get more messages initially for better filtering
      mode: 'recent' // Get most recent messages
    });

    // Filter and sort messages
    let messages = (result.results || [])
      .filter(item => item.source_type === 'conversation' && item.content)
      .sort((a, b) => new Date(a.created_at || 0) - new Date(b.created_at || 0))
      .map(item => ({
        role: item.role || 'user',
        content: item.content || item.snippet || '',
        message_id: item.message_id,
        turn_id: item.turn_id,
        created_at: item.created_at
      }))
      .slice(-maxMessages); // Keep only the most recent messages

    // Calculate token usage for system prompt if needed
    let systemPromptTokens = 0;
    if (includeSystemPrompt) {
      systemPromptTokens = estimateTokenCount(SYSTEM_PROMPT);
    }

    // Truncate messages to fit within token limit
    const availableTokens = maxTokens - systemPromptTokens;
    messages = truncateMessagesToTokenLimit(messages, availableTokens);
    
    // Create a detailed summary of the conversation context
    const totalTokens = messages.reduce((sum, msg) => sum + estimateTokenCount(msg.content), 0);
    const summary = messages.length > 0
      ? `对话包含 ${messages.length} 条消息，约 ${totalTokens} tokens，涵盖了用户的问题和AI的回复。`
      : '这是一个新的对话。';
    
    const metadata = {
      message_count: messages.length,
      estimated_tokens: totalTokens,
      system_prompt_tokens: systemPromptTokens,
      total_tokens: totalTokens + systemPromptTokens,
      truncated: (result.results || []).length > messages.length
    };
    
    telemetry.emit('ai.context_built', {
      topic_id: topicId,
      ...metadata
    });
    
    // If requested, include the system prompt as the first message in the messages array
    const outputMessages = includeSystemPrompt
      ? [{ role: 'system', content: SYSTEM_PROMPT }, ...messages]
      : messages;
    
    return { messages: outputMessages, summary, metadata };
  } catch (error) {
    telemetry.emit('ai.context_build_failed', {
      topic_id: topicId,
      error: error.message
    });

    // Return empty context on failure
    return {
      messages: [],
      summary: '无法获取对话历史。',
      metadata: {
        message_count: 0,
        estimated_tokens: 0,
        system_prompt_tokens: 0,
        total_tokens: 0,
        truncated: false,
        error: error.message
      }
    };
  }
}

/**
 * Classify error type for appropriate handling
 * @param {Error} error - The error to classify
 * @returns {string} Error type from AI_ERROR_TYPES
 */
function classifyError(error) {
  if (!error) return AI_ERROR_TYPES.API_ERROR;
  
  const message = error.message?.toLowerCase() || '';
  const status = error.status || error.response?.status;
  
  if (status === 429 || message.includes('rate limit')) {
    return AI_ERROR_TYPES.RATE_LIMIT;
  }
  if (message.includes('timeout') || error.code === 'ECONNABORTED') {
    return AI_ERROR_TYPES.TIMEOUT;
  }
  if (message.includes('context') && message.includes('too long')) {
    return AI_ERROR_TYPES.CONTEXT_TOO_LONG;
  }
  if (message.includes('network') || message.includes('connection')) {
    return AI_ERROR_TYPES.NETWORK_ERROR;
  }
  
  return AI_ERROR_TYPES.API_ERROR;
}

/**
 * Get fallback response based on error type
 * @param {string} errorType - Error type from AI_ERROR_TYPES
 * @returns {string} Appropriate fallback response
 */
function getFallbackResponse(errorType) {
  switch (errorType) {
    case AI_ERROR_TYPES.RATE_LIMIT:
      return "系统当前请求较多，请稍后再试。";
    case AI_ERROR_TYPES.TIMEOUT:
      return "响应超时，请重新提问或简化您的问题。";
    case AI_ERROR_TYPES.CONTEXT_TOO_LONG:
      return "对话历史较长，我将基于最近的内容回复您。";
    case AI_ERROR_TYPES.NETWORK_ERROR:
      return "网络连接问题，请检查网络后重试。";
    default:
      return FALLBACK_RESPONSES[Math.floor(Math.random() * FALLBACK_RESPONSES.length)];
  }
}

/**
 * Calculate delay for exponential backoff
 * @param {number} attempt - Current attempt number (0-based)
 * @returns {number} Delay in milliseconds
 */
function calculateBackoffDelay(attempt) {
  const baseDelay = 1000; // 1 second
  const maxDelay = 10000; // 10 seconds
  const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
  // Add jitter to prevent thundering herd
  return delay + Math.random() * 1000;
}

/**
 * Generate AI response with retry logic and error handling
 * @param {number} topicId - Topic ID for context
 * @param {string} userMessage - User's message
 * @param {Object} options - Generation options
 * @returns {Promise<{content: string, metadata: Object}>}
 */
async function generateAIResponse(topicId, userMessage, options = {}) {
  const startTime = Date.now();
  const {
    model = DEFAULT_MODEL,
    maxTokens = DEFAULT_MAX_TOKENS,
    temperature = DEFAULT_TEMPERATURE,
    includeContext = true,
    client = null
  } = options;

  let lastError = null;
  
  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
    try {
      // Build conversation context if requested
      let contextMessages = [];
      let contextMetadata = {};
      if (includeContext) {
        const context = await buildConversationContext(topicId, MAX_CONTEXT_MESSAGES, {
          maxTokens: Math.floor(maxTokens * 0.6), // Reserve 60% of tokens for context
          includeSystemPrompt: true
        });
        contextMessages = context.messages;
        contextMetadata = context.metadata;
      }

      // Prepare messages for OpenAI API
      const messages = [
        { role: 'system', content: SYSTEM_PROMPT },
        ...contextMessages,
        { role: 'user', content: userMessage }
      ];

      // Get OpenAI client
      const openaiClient = client || getOpenAIClient();
      
      // Generate response with timeout
      const response = await Promise.race([
        openaiClient.chat.completions.create({
          model,
          messages,
          max_tokens: maxTokens,
          temperature,
          stream: false
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), AI_RESPONSE_TIMEOUT)
        )
      ]);

      const content = response.choices?.[0]?.message?.content;
      if (!content) {
        throw new Error('Invalid response: no content generated');
      }

      const duration = Date.now() - startTime;
      const metadata = {
        model,
        tokens_used: response.usage?.total_tokens || 0,
        duration_ms: duration,
        attempt: attempt + 1,
        context_messages: contextMessages.length,
        context_metadata: contextMetadata
      };

      telemetry.emit('ai.response_generated', {
        topic_id: topicId,
        success: true,
        duration_ms: duration,
        tokens_used: metadata.tokens_used,
        attempt: attempt + 1,
        model
      });

      return { content, metadata };

    } catch (error) {
      lastError = error;
      const errorType = classifyError(error);
      
      telemetry.emit('ai.response_failed', {
        topic_id: topicId,
        attempt: attempt + 1,
        error_type: errorType,
        error_message: error.message,
        duration_ms: Date.now() - startTime
      });

      // Don't retry for certain error types
      if (errorType === AI_ERROR_TYPES.CONTEXT_TOO_LONG && attempt === 0) {
        // Try once more with reduced context
        continue;
      }
      
      if (errorType === AI_ERROR_TYPES.API_ERROR && attempt < MAX_RETRIES - 1) {
        // Wait before retrying
        const delay = calculateBackoffDelay(attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      if (errorType === AI_ERROR_TYPES.RATE_LIMIT && attempt < MAX_RETRIES - 1) {
        // Longer wait for rate limits
        const delay = calculateBackoffDelay(attempt) * 2;
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      // For other errors or final attempt, break and use fallback
      break;
    }
  }

  // All retries failed, use fallback response
  const errorType = classifyError(lastError);
  const fallbackContent = getFallbackResponse(errorType);
  const duration = Date.now() - startTime;
  
  telemetry.emit('ai.response_fallback', {
    topic_id: topicId,
    error_type: errorType,
    fallback_used: true,
    duration_ms: duration
  });

  return {
    content: fallbackContent,
    metadata: {
      fallback: true,
      error_type: errorType,
      duration_ms: duration,
      attempts: MAX_RETRIES
    }
  };
}

module.exports = {
  generateAIResponse,
  buildConversationContext,
  AI_ERROR_TYPES,
  // Export for testing
  classifyError,
  getFallbackResponse,
  calculateBackoffDelay,
  estimateTokenCount,
  truncateMessagesToTokenLimit
};
