/*
Comprehensive Unit Tests for AI Response Service
Tests all aspects of AI service including success, rate limits, timeouts, retries, and edge cases
*/

// Mock dependencies
jest.mock('../src/lib/openai_client');
jest.mock('../src/services/retrieval_service');
jest.mock('../src/lib/telemetry');

const {
  generateAIResponse,
  estimateTokenCount,
  truncateMessagesToTokenLimit,
  buildConversationContext
} = require('../src/services/ai_response_service');
const { getOpenAIClient } = require('../src/lib/openai_client');
const { searchContext } = require('../src/services/retrieval_service');
const telemetry = require('../src/lib/telemetry');

describe('AI Response Service - Comprehensive Tests', () => {
  let mockOpenAI;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock OpenAI client
    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    };

    getOpenAIClient.mockReturnValue(mockOpenAI);

    // Mock retrieval service
    searchContext.mockResolvedValue({
      results: [
        { snippet: 'Previous conversation context', score: 0.9, source_type: 'conversation' }
      ]
    });

    // Mock telemetry
    telemetry.recordEvent = jest.fn();
  });

  describe('generateAIResponse - Success Scenarios', () => {
    test('should generate successful response with all metadata', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: '这是一个详细的AI回复，包含了用户问题的完整答案。'
          }
        }],
        usage: {
          total_tokens: 150,
          prompt_tokens: 80,
          completion_tokens: 70
        },
        model: 'gpt-3.5-turbo'
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await generateAIResponse(123, '什么是机器学习？', {
        includeContext: true,
        maxTokens: 2000,
        temperature: 0.7
      });

      expect(result.content).toBe('这是一个详细的AI回复，包含了用户问题的完整答案。');
      expect(result.metadata).toMatchObject({
        tokens_used: 150,
        prompt_tokens: 80,
        completion_tokens: 70,
        model: 'gpt-3.5-turbo',
        attempt: 1,
        fallback: false
      });

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-3.5-turbo',
        messages: expect.any(Array),
        max_tokens: 2000,
        temperature: 0.7
      });
    });

    test('should handle response without context when includeContext is false', async () => {
      const mockResponse = {
        choices: [{ message: { content: '简单回复' } }],
        usage: { total_tokens: 50 }
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await generateAIResponse(123, '你好', {
        includeContext: false
      });

      expect(result.content).toBe('简单回复');
      expect(result.metadata.tokens_used).toBe(50);

      // Should not call searchContext when includeContext is false
      expect(searchContext).not.toHaveBeenCalled();
    });

    test('should use custom model and parameters', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'GPT-4 response' } }],
        usage: { total_tokens: 200 }
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      await generateAIResponse(123, '测试GPT-4', {
        model: 'gpt-4',
        temperature: 0.9,
        maxTokens: 3000
      });

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-4',
        messages: expect.any(Array),
        max_tokens: 3000,
        temperature: 0.9
      });
    });
  });

  describe('generateAIResponse - Rate Limiting and Retries', () => {
    test('should retry on 429 rate limit and succeed', async () => {
      const rateLimitError = { status: 429, message: 'Rate limit exceeded' };
      const successResponse = {
        choices: [{ message: { content: '重试成功的回复' } }],
        usage: { total_tokens: 100 }
      };

      mockOpenAI.chat.completions.create
        .mockRejectedValueOnce(rateLimitError)
        .mockResolvedValue(successResponse);

      const result = await generateAIResponse(123, '测试重试', {
        includeContext: false
      });

      expect(result.content).toBe('重试成功的回复');
      expect(result.metadata.attempt).toBe(2);
      expect(result.metadata.fallback).toBe(false);
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(2);
    });

    test('should retry multiple times on consecutive rate limits', async () => {
      const rateLimitError = { status: 429, message: 'Rate limit exceeded' };
      const successResponse = {
        choices: [{ message: { content: '多次重试后成功' } }],
        usage: { total_tokens: 120 }
      };

      mockOpenAI.chat.completions.create
        .mockRejectedValueOnce(rateLimitError)
        .mockRejectedValueOnce(rateLimitError)
        .mockRejectedValueOnce(rateLimitError)
        .mockResolvedValue(successResponse);

      const result = await generateAIResponse(123, '测试多次重试', {
        includeContext: false
      });

      expect(result.content).toBe('多次重试后成功');
      expect(result.metadata.attempt).toBe(4);
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(4);
    });

    test('should fallback after maximum retries on rate limit', async () => {
      const rateLimitError = { status: 429, message: 'Rate limit exceeded' };

      mockOpenAI.chat.completions.create.mockRejectedValue(rateLimitError);

      const result = await generateAIResponse(123, '测试最大重试', {
        includeContext: false
      });

      expect(result.metadata.fallback).toBe(true);
      expect(result.metadata.error_type).toBe('rate_limit');
      expect(result.content).toContain('抱歉');
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(5); // 1 + 4 retries
    });
  });

  describe('generateAIResponse - Timeout and Network Errors', () => {
    test('should handle timeout errors with fallback', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.code = 'ECONNABORTED';

      mockOpenAI.chat.completions.create.mockRejectedValue(timeoutError);

      const result = await generateAIResponse(123, '测试超时', {
        includeContext: false
      });

      expect(result.metadata.fallback).toBe(true);
      expect(result.metadata.error_type).toBe('timeout');
      expect(result.content).toContain('网络超时');
    });

    test('should handle network connection errors', async () => {
      const networkError = new Error('Network error');
      networkError.code = 'ENOTFOUND';

      mockOpenAI.chat.completions.create.mockRejectedValue(networkError);

      const result = await generateAIResponse(123, '测试网络错误', {
        includeContext: false
      });

      expect(result.metadata.fallback).toBe(true);
      expect(result.metadata.error_type).toBe('network');
      expect(result.content).toContain('网络连接');
    });

    test('should handle API key errors', async () => {
      const authError = { status: 401, message: 'Invalid API key' };

      mockOpenAI.chat.completions.create.mockRejectedValue(authError);

      const result = await generateAIResponse(123, '测试认证错误', {
        includeContext: false
      });

      expect(result.metadata.fallback).toBe(true);
      expect(result.metadata.error_type).toBe('auth');
      expect(result.content).toContain('服务配置');
    });

    test('should handle server errors (500)', async () => {
      const serverError = { status: 500, message: 'Internal server error' };

      mockOpenAI.chat.completions.create.mockRejectedValue(serverError);

      const result = await generateAIResponse(123, '测试服务器错误', {
        includeContext: false
      });

      expect(result.metadata.fallback).toBe(true);
      expect(result.metadata.error_type).toBe('server');
      expect(result.content).toContain('服务暂时不可用');
    });
  });